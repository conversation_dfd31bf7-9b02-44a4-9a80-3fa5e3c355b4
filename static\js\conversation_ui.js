/**
 * 对话式8D报告系统 - 用户界面交互
 * 处理标签栏、对话历史、版本切换等UI交互逻辑
 */

class ConversationUI {
    constructor() {
        this.isInitialized = false;
        this.currentConversationId = null;
        this.isConversationHistoryVisible = false;
        
        this.bindUIEvents();
        this.initConversationManager();
    }

    /**
     * 初始化对话管理器事件监听
     */
    initConversationManager() {
        if (!window.conversationManager) return;

        // 监听对话管理器事件
        window.conversationManager.addEventListener('conversationCreated', (conversation) => {
            this.onConversationCreated(conversation);
        });

        window.conversationManager.addEventListener('conversationLoaded', (conversation) => {
            this.onConversationLoaded(conversation);
        });

        window.conversationManager.addEventListener('versionAdded', ({ conversation, version }) => {
            this.onVersionAdded(conversation, version);
        });

        window.conversationManager.addEventListener('versionSwitched', ({ conversation, version }) => {
            this.onVersionSwitched(conversation, version);
        });

        window.conversationManager.addEventListener('messageAdded', ({ conversation, message }) => {
            this.onMessageAdded(conversation, message);
        });

        window.conversationManager.addEventListener('versionDeleted', ({ conversation, deletedVersionId, newActiveVersion }) => {
            this.onVersionDeleted(conversation, deletedVersionId, newActiveVersion);
        });

        // 初始化滚动监听 - 已禁用，由d8_form.js中的initScrollTracking处理
        // this.initScrollListener();

        // 初始化完成
        this.isInitialized = true;
        console.log('对话UI初始化完成');
    }

    /**
     * 绑定UI事件
     */
    bindUIEvents() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupUIEventListeners();
            });
        } else {
            this.setupUIEventListeners();
        }
    }

    /**
     * 设置UI事件监听器
     */
    setupUIEventListeners() {
        console.log('设置UI事件监听器');

        // 初始化响应式侧边栏
        this.initResponsiveSidebar();

        // 初始化右侧导航 - 已禁用，由d8_form.js中的initNavigation处理
        // setTimeout(() => {
        //     if (typeof initRightNavigation === 'function') {
        //         initRightNavigation();
        //     }
        // }, 100);

        // 版本标签点击事件
        const versionTabs = document.getElementById('version-tabs');
        if (versionTabs) {
            versionTabs.addEventListener('click', (e) => {
                const versionTab = e.target.closest('.version-tab');
                if (versionTab) {
                    const versionId = versionTab.dataset.versionId;
                    console.log('点击版本标签:', versionId, '当前活跃:', versionTab.classList.contains('active'));

                    if (!versionTab.classList.contains('active')) {
                        this.switchToVersion(versionId);
                    } else {
                        console.log('已经是活跃版本，无需切换');
                    }
                }
            });
        } else {
            console.warn('版本标签容器未找到，将延迟绑定事件');
            // 延迟绑定，等待DOM完全加载
            setTimeout(() => {
                const laterTabs = document.getElementById('version-tabs');
                if (laterTabs) {
                    console.log('延迟绑定版本标签事件成功');
                    laterTabs.addEventListener('click', (e) => {
                        const versionTab = e.target.closest('.version-tab');
                        if (versionTab) {
                            const versionId = versionTab.dataset.versionId;
                            console.log('延迟绑定-点击版本标签:', versionId);

                            if (!versionTab.classList.contains('active')) {
                                this.switchToVersion(versionId);
                            }
                        }
                    });
                }
            }, 1000);
        }

        // 对话历史面板关闭
        const closeBtn = document.querySelector('.close-panel-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideConversationHistory();
            });
        }

        // 点击侧边栏外部关闭（移动端）
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('unified-sidebar');
            const toggleBtn = document.getElementById('floating-sidebar-toggle');

            if (sidebar && sidebar.classList.contains('mobile-open') &&
                !sidebar.contains(e.target) &&
                toggleBtn && !toggleBtn.contains(e.target)) {
                this.closeSidebar();
            }
        });

        // ESC键关闭侧边栏
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
            }
        });
    }

    /**
     * 初始化响应式侧边栏
     */
    initResponsiveSidebar() {
        const handleResize = () => {
            const floatingToggle = document.getElementById('floating-sidebar-toggle');
            const sidebar = document.getElementById('unified-sidebar');
            const body = document.body;

            if (window.innerWidth <= 1200) {
                // 中等屏幕：显示悬浮按钮，隐藏侧边栏
                if (floatingToggle) {
                    floatingToggle.style.display = 'flex';
                }
                if (sidebar) {
                    sidebar.classList.add('collapsed');
                }
                body.classList.add('sidebar-collapsed');
            } else {
                // 大屏幕：隐藏悬浮按钮，显示侧边栏
                if (floatingToggle) {
                    floatingToggle.style.display = 'none';
                }
                if (sidebar) {
                    sidebar.classList.remove('collapsed', 'mobile-open');
                }
                body.classList.remove('sidebar-collapsed');
                this.closeSidebar(); // 确保遮罩层被关闭
            }
        };

        // 初始化
        handleResize();

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
    }

    /**
     * 创建新对话
     */
    async createNewConversation() {
        let conversationCreated = false;
        let conversationId = null;

        try {
            console.log('开始创建新对话...');

            // 检查对话管理器是否存在
            if (!window.conversationManager) {
                console.error('对话管理器未初始化');
                alert('系统未正确初始化，请刷新页面重试');
                return;
            }

            // 先创建对话，再清空表单，避免表单清空失败影响对话创建
            console.log('创建对话...');
            conversationId = window.conversationManager.createConversation(null);
            console.log('对话创建成功，ID:', conversationId);

            // 验证对话ID是否有效
            if (!conversationId) {
                console.error('createConversation返回了无效的ID');
                alert('创建对话失败：无法生成对话ID，请重试');
                return;
            }

            // 标记对话已创建
            conversationCreated = true;
            this.currentConversationId = conversationId;

            // 获取创建的对话，添加重试机制
            let conversation = window.conversationManager.conversations.get(conversationId);
            let retryCount = 0;
            const maxRetries = 3;

            while (!conversation && retryCount < maxRetries) {
                console.warn(`第${retryCount + 1}次尝试获取对话失败，重试中...`);
                // 短暂延迟后重试
                await new Promise(resolve => setTimeout(resolve, 50));
                conversation = window.conversationManager.conversations.get(conversationId);
                retryCount++;
            }

            if (!conversation) {
                console.error('无法获取创建的对话，经过重试仍然失败');
                console.error('对话管理器状态:', {
                    conversationsSize: window.conversationManager.conversations.size,
                    conversationId: conversationId,
                    allConversationIds: Array.from(window.conversationManager.conversations.keys())
                });

                // 尝试恢复：重新创建对话
                console.log('尝试恢复：重新创建对话...');
                try {
                    // 清理可能的残留状态
                    this.currentConversationId = null;

                    // 重新创建
                    conversationId = window.conversationManager.createConversation(null);
                    conversation = window.conversationManager.conversations.get(conversationId);
                    this.currentConversationId = conversationId;

                    if (!conversation) {
                        throw new Error('恢复创建也失败了');
                    }
                    console.log('恢复成功，对话已创建');
                } catch (recoveryError) {
                    console.error('恢复创建失败:', recoveryError);
                    alert('创建对话遇到问题，但系统已尝试恢复。如果问题持续，请刷新页面重试。');
                    return;
                }
            }

            // 对话创建成功，现在处理表单和界面
            console.log('获取对话成功，创建初始版本...');

            // 手动创建初始版本（空白版本）
            const initialFormData = {};
            window.conversationManager.addFormVersion(conversation, initialFormData, 'user');
            console.log('初始版本创建成功');

            // 清空表单（放在对话创建成功之后，避免表单清空失败影响对话创建）
            try {
                console.log('清空表单...');
                this.clearFormCompletely();
                console.log('表单清空成功');
            } catch (formError) {
                console.warn('表单清空失败，但对话已创建成功:', formError);
                // 表单清空失败不影响对话创建，只记录警告
            }

            // 渲染版本列表
            try {
                console.log('渲染版本列表...');
                this.renderVersionList(conversation);
                console.log('版本列表渲染成功');
            } catch (renderError) {
                console.warn('版本列表渲染失败:', renderError);
            }

            // 刷新界面
            try {
                console.log('刷新对话列表...');
                this.refreshConversationList();
                console.log('对话列表刷新成功');
            } catch (refreshError) {
                console.warn('对话列表刷新失败:', refreshError);
            }

            console.log('创建新对话完成:', conversationId);
            console.log('✅ 新对话创建成功！');

            // 显示成功提示
            this.showSuccessMessage('新对话创建成功！');

        } catch (error) {
            console.error('创建新对话过程中出现错误:', error);
            console.error('错误堆栈:', error.stack);

            // 只有在对话真正创建失败时才显示错误信息
            if (!conversationCreated) {
                alert('创建新对话失败，请重试。错误详情：' + error.message);
            } else {
                console.warn('对话已创建成功，但后续处理出现问题:', error.message);
                // 对话已创建成功，只是后续处理有问题，不显示失败提示
                this.showWarningMessage('对话已创建，但界面更新可能不完整，请刷新页面查看。');
            }
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        console.log('✅ ' + message);
        this.showToast(message, 'success');
    }

    /**
     * 显示警告消息
     */
    showWarningMessage(message) {
        console.warn('⚠️ ' + message);
        this.showToast(message, 'warning');
    }

    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'warning' ? '#FF9800' : '#2196F3'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 添加新版本
     */
    addNewVersion() {
        try {
            console.log('开始添加新版本...');

            // 检查是否有当前对话
            if (!this.currentConversationId) {
                this.showWarningMessage('请先创建或选择一个对话');
                return;
            }

            // 获取当前对话
            const conversation = window.conversationManager.conversations.get(this.currentConversationId);
            if (!conversation) {
                this.showWarningMessage('当前对话不存在，请重新选择');
                return;
            }

            // 收集当前表单数据
            let currentFormData = {};
            if (typeof collectFormData === 'function') {
                try {
                    currentFormData = collectFormData();
                    console.log('收集到的表单数据:', currentFormData);
                } catch (error) {
                    console.warn('收集表单数据失败，使用空数据:', error);
                    currentFormData = {};
                }
            }

            // 创建新版本
            const versionId = window.conversationManager.addFormVersion(
                conversation,
                currentFormData,
                'user'
            );

            console.log('新版本创建成功:', versionId);

            // 更新UI
            this.renderVersionList(conversation);
            this.showVersionTabs();

            // 显示成功提示
            this.showSuccessMessage('新版本创建成功！');

            console.log('✅ 新版本添加完成');

        } catch (error) {
            console.error('添加新版本失败:', error);
            this.showWarningMessage('添加新版本失败：' + error.message);
        }
    }

    /**
     * 显示对话历史
     */
    showConversationHistory() {
        const panel = document.getElementById('conversation-history-panel');
        if (panel) {
            panel.style.display = 'flex';
            this.isConversationHistoryVisible = true;
            this.refreshConversationList();
        }
    }

    /**
     * 隐藏对话历史
     */
    hideConversationHistory() {
        const panel = document.getElementById('conversation-history-panel');
        if (panel) {
            panel.style.display = 'none';
            this.isConversationHistoryVisible = false;
        }
    }

    /**
     * 刷新对话列表
     */
    refreshConversationList() {
        const conversationList = document.getElementById('conversation-list');
        const noConversations = document.getElementById('no-conversations');

        if (!conversationList) return;

        const conversations = window.conversationManager.getAllConversations();

        if (conversations.length === 0) {
            if (noConversations) {
                noConversations.style.display = 'block';
            }
            conversationList.innerHTML = '<div class="no-conversations">暂无对话记录</div>';
            return;
        }

        if (noConversations) {
            noConversations.style.display = 'none';
        }

        // 清空现有列表
        conversationList.innerHTML = '';

        // 按创建时间倒序排列，最新的在最上面
        const sortedConversations = conversations.sort((a, b) => {
            return new Date(b.created_at) - new Date(a.created_at);
        });

        sortedConversations.forEach(conversation => {
            const item = document.createElement('div');
            item.className = 'conversation-item';
            item.dataset.conversationId = conversation.conversation_id;

            if (conversation.conversation_id === this.currentConversationId) {
                item.classList.add('active');
            }

            // 格式化时间戳
            const timestamp = new Date(conversation.created_at).toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            item.innerHTML = `
                <div class="conversation-icon">💬</div>
                <div class="conversation-content">
                    <div class="conversation-title">${conversation.title}</div>
                    <div class="conversation-timestamp">${timestamp}</div>
                </div>
                <button class="conversation-delete-btn" onclick="event.stopPropagation(); conversationUI.deleteConversation('${conversation.conversation_id}')">
                    ×
                </button>
            `;

            item.addEventListener('click', () => {
                this.loadConversation(conversation.conversation_id);
                // 在移动端加载对话后关闭侧边栏
                if (window.innerWidth <= 1200) {
                    this.closeSidebar();
                }
            });

            conversationList.appendChild(item);
        });
    }

    /**
     * 创建对话列表项
     */
    createConversationItem(conversation) {
        const item = document.createElement('div');
        item.className = 'conversation-item';
        item.dataset.conversationId = conversation.conversation_id;

        // 添加活跃状态
        if (this.currentConversationId === conversation.conversation_id) {
            item.classList.add('active');
        }

        const createdAt = new Date(conversation.created_at).toLocaleString('zh-CN');
        const updatedAt = new Date(conversation.updated_at).toLocaleString('zh-CN');
        const versionCount = conversation.form_versions.length;
        const messageCount = conversation.chat_history.length;

        item.innerHTML = `
            <div class="conversation-info">
                <div class="conversation-title">${conversation.title}</div>
                <div class="conversation-meta">
                    <div>创建时间：${createdAt}</div>
                    ${updatedAt !== createdAt ? `<div>更新时间：${updatedAt}</div>` : ''}
                </div>
                <div class="conversation-stats">
                    <div class="conversation-stat">
                        <span>🏷️</span>
                        <span>${versionCount} 个版本</span>
                    </div>
                    <div class="conversation-stat">
                        <span>💬</span>
                        <span>${messageCount} 条消息</span>
                    </div>
                </div>
            </div>
            <div class="conversation-actions">
                <button class="conversation-action-btn load-conversation-btn" 
                        onclick="conversationUI.loadConversation('${conversation.conversation_id}')">
                    加载
                </button>
                <button class="conversation-action-btn delete-conversation-btn" 
                        onclick="conversationUI.deleteConversation('${conversation.conversation_id}')">
                    删除
                </button>
            </div>
        `;

        return item;
    }

    /**
     * 加载对话
     */
    loadConversation(conversationId) {
        try {
            if (window.conversationManager.loadConversation(conversationId)) {
                this.currentConversationId = conversationId;
                this.hideConversationHistory();
                console.log('加载对话成功:', conversationId);
            } else {
                alert('加载对话失败');
            }
        } catch (error) {
            console.error('加载对话失败:', error);
            alert('加载对话失败：' + error.message);
        }
    }

    /**
     * 显示自定义确认对话框
     */
    showCustomConfirm(message, title = '确定删除对话?') {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'custom-confirm-modal';
        modal.innerHTML = `
            <div class="custom-confirm-content">
                <button class="custom-confirm-close">×</button>
                <div class="custom-confirm-header">
                    <div class="custom-confirm-icon">⚠</div>
                    <h3 class="custom-confirm-title">${title}</h3>
                </div>
                <div class="custom-confirm-message">${message}</div>
                <div class="custom-confirm-buttons">
                    <button class="custom-confirm-btn cancel-btn">取消</button>
                    <button class="custom-confirm-btn confirm-btn">删除</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        return new Promise((resolve) => {
            const confirmBtn = modal.querySelector('.confirm-btn');
            const cancelBtn = modal.querySelector('.cancel-btn');
            const closeBtn = modal.querySelector('.custom-confirm-close');

            const cleanup = () => {
                document.body.removeChild(modal);
            };

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            closeBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    cleanup();
                    resolve(false);
                }
            });
        });
    }

    /**
     * 删除对话
     */
    async deleteConversation(conversationId) {
        try {
            const conversation = window.conversationManager.conversations.get(conversationId);
            if (!conversation) return;

            const confirmMsg = `删除后，聊天记录将不可恢复。`;
            if (await this.showCustomConfirm(confirmMsg)) {
                if (window.conversationManager.deleteConversation(conversationId)) {
                    // 如果删除的是当前对话，加载上一条记录
                    if (this.currentConversationId === conversationId) {
                        this.currentConversationId = null;

                        // 获取剩余的对话列表
                        const conversations = Array.from(window.conversationManager.conversations.values())
                            .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));

                        if (conversations.length > 0) {
                            // 加载最新的对话
                            this.loadConversation(conversations[0].conversation_id);
                        } else {
                            // 没有其他对话时才创建新对话
                            this.createNewConversation();
                        }
                    }
                    this.refreshConversationList();
                    console.log('删除对话成功:', conversationId);
                } else {
                    alert('删除对话失败');
                }
            }
        } catch (error) {
            console.error('删除对话失败:', error);
            alert('删除对话失败：' + error.message);
        }
    }

    /**
     * 切换到指定版本
     */
    switchToVersion(versionId) {
        try {
            console.log('=== 开始版本切换 ===');
            console.log('目标版本ID:', versionId);
            console.log('当前对话ID:', this.currentConversationId);
            
            if (!this.currentConversationId) {
                console.error('没有当前对话ID');
                alert('请先创建或加载一个对话');
                return;
            }
            
            const conversation = window.conversationManager.conversations.get(this.currentConversationId);
            if (!conversation) {
                console.error('找不到当前对话');
                alert('找不到当前对话');
                return;
            }
            
            const version = conversation.form_versions.find(v => v.version_id === versionId);
            if (!version) {
                console.error('找不到指定版本:', versionId);
                console.log('可用版本:', conversation.form_versions.map(v => v.version_id));
                alert('找不到指定版本');
                return;
            }
            
            console.log('找到版本:', {
                version_id: version.version_id,
                version_name: version.version_name,
                created_by: version.created_by,
                data_keys: Object.keys(version.form_data || {}),
                data_count: Object.keys(version.form_data || {}).length
            });
            
            // 调用ConversationManager的版本切换
            if (window.conversationManager.switchToVersion(versionId)) {
                console.log('ConversationManager版本切换成功');
                console.log('=== 版本切换完成 ===');
            } else {
                console.error('ConversationManager.switchToVersion 返回false');
                alert('版本切换失败');
            }
        } catch (error) {
            console.error('版本切换异常:', error);
            console.error('错误堆栈:', error.stack);
            alert('版本切换失败：' + error.message);
        }
    }

    /**
     * 显示版本标签栏
     */
    showVersionTabs() {
        const container = document.getElementById('version-tabs-container');
        if (container) {
            container.style.display = 'flex';
        }
    }

    /**
     * 隐藏版本标签栏
     */
    hideVersionTabs() {
        const container = document.getElementById('version-tabs-container');
        if (container) {
            container.style.display = 'none';
        }
    }

    /**
     * 渲染版本标签
     */
    renderVersionTabs(conversation) {
        const container = document.getElementById('version-tabs');
        if (!container || !conversation) return;

        container.innerHTML = '';

        conversation.form_versions.forEach(version => {
            const tab = this.createVersionTab(version);
            container.appendChild(tab);
        });
    }

    /**
     * 渲染版本列表（侧边栏中的版本记录）
     */
    renderVersionList(conversation) {
        const versionSection = document.getElementById('version-history-section');
        const versionList = document.getElementById('version-list');

        if (!versionList) return;

        if (conversation) {
            // 有对话时显示版本区域（即使没有版本，也要显示新增版本按钮）
            if (versionSection) {
                versionSection.style.display = 'block';
            }

            // 清空现有版本列表
            versionList.innerHTML = '';

            if (conversation.form_versions.length > 0) {
                // 渲染所有版本
                conversation.form_versions.forEach(version => {
                    const item = this.createVersionListItem(version);
                    versionList.appendChild(item);
                });
            } else {
                // 没有版本时显示提示信息
                const emptyState = document.createElement('div');
                emptyState.className = 'no-versions';
                emptyState.textContent = '暂无版本记录';
                versionList.appendChild(emptyState);
            }
        } else {
            // 没有对话时隐藏版本区域
            if (versionSection) {
                versionSection.style.display = 'none';
            }
        }
    }

    /**
     * 创建版本列表项
     */
    createVersionListItem(version) {
        const item = document.createElement('div');
        item.className = 'version-item';
        item.dataset.versionId = version.version_id;

        if (version.is_active) {
            item.classList.add('active');
        }

        const createdAt = new Date(version.created_at).toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        const versionIcon = version.created_by === 'ai' ? '🤖' : '👤';

        item.innerHTML = `
            <div class="version-icon">${versionIcon}</div>
            <div class="version-info">
                <div class="version-name">${version.version_name}</div>
                <div class="version-meta">${createdAt}</div>
            </div>
            <button class="version-delete-btn" onclick="event.stopPropagation(); conversationUI.deleteVersion('${version.version_id}', event)">×</button>
        `;

        item.addEventListener('click', () => {
            this.switchToVersion(version.version_id);
        });

        return item;
    }

    /**
     * 创建版本标签
     */
    createVersionTab(version) {
        const tab = document.createElement('div');
        tab.className = `version-tab ${version.created_by}`;
        tab.dataset.versionId = version.version_id;

        if (version.is_active) {
            tab.classList.add('active');
        }

        const createdAt = new Date(version.created_at).toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        tab.innerHTML = `
            <div class="version-tab-icon"></div>
            <div class="version-tab-content">
                <div class="version-tab-title">${version.version_name}</div>
                <div class="version-tab-meta">
                    <span>${createdAt}</span>
                </div>
            </div>
            <button class="version-tab-close" onclick="conversationUI.deleteVersion('${version.version_id}', event)" title="删除版本">×</button>
        `;

        return tab;
    }

    /**
     * 删除版本
     */
    deleteVersion(versionId, event) {
        try {
            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
            }
            
            if (!this.currentConversationId) {
                alert('没有当前对话');
                return;
            }

            const conversation = window.conversationManager.conversations.get(this.currentConversationId);
            if (!conversation) {
                alert('找不到当前对话');
                return;
            }

            const version = conversation.form_versions.find(v => v.version_id === versionId);
            if (!version) {
                alert('找不到指定版本');
                return;
            }

            // 不能删除唯一的版本
            if (conversation.form_versions.length <= 1) {
                alert('不能删除最后一个版本，每个对话至少需要保留一个版本');
                return;
            }

            const confirmMsg = `确定要删除版本"${version.version_name}"吗？此操作不可撤销。`;
            if (confirm(confirmMsg)) {
                if (window.conversationManager.deleteVersion(versionId)) {
                    console.log('版本删除成功:', versionId);
                    // UI会通过事件监听器自动更新
                } else {
                    alert('删除版本失败');
                }
            }
        } catch (error) {
            console.error('删除版本失败:', error);
            alert('删除版本失败：' + error.message);
        }
    }

    /**
     * 更新对话标题
     */
    updateConversationTitle(title) {
        const titleElement = document.getElementById('conversation-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    /**
     * 更新对话副标题
     */
    updateConversationSubtitle(subtitle) {
        const subtitleElement = document.getElementById('conversation-subtitle');
        if (subtitleElement) {
            subtitleElement.textContent = subtitle;
        }
    }



    /**
     * 收集当前表单数据
     */
    collectCurrentFormData() {
        // 这里需要与现有的表单收集逻辑集成
        if (typeof collectFormData === 'function') {
            return collectFormData();
        }
        return null;
    }

    /**
     * 清空表单
     */
    clearForm() {
        // 这里需要与现有的表单清空逻辑集成
        if (typeof clearForm === 'function') {
            clearForm();
        }
    }

    /**
     * 更新版本高亮状态
     */
    updateVersionHighlight(activeVersionId) {
        const versionItems = document.querySelectorAll('.version-item');
        versionItems.forEach(item => {
            const versionId = item.dataset.versionId;
            if (versionId === activeVersionId) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });

        // 同时更新右侧导航栏高亮 - 已禁用，由d8_form.js中的setActiveSection处理
        // this.updateNavigationHighlight();
    }

    /**
     * 更新右侧导航栏高亮状态
     */
    updateNavigationHighlight() {
        // 此方法已禁用，导航栏高亮由d8_form.js中的setActiveSection函数统一处理
        // 避免与d8_form.js中的高亮逻辑冲突
        return;
    }

    /**
     * 初始化滚动监听 - 已禁用，由d8_form.js中的initScrollTracking处理
     */
    initScrollListener() {
        // 此方法已禁用，滚动监听由d8_form.js中的initScrollTracking函数统一处理
        // 避免与d8_form.js中的滚动监听逻辑冲突
        return;
    }

    /**
     * 填充表单数据
     */
    populateForm(formData) {
        console.log('开始填充表单数据:', formData);
        
        if (!formData || typeof formData !== 'object') {
            console.log('无有效数据需要填充');
            return;
        }
        
        // 显示当前版本的修改意见
        this.displayModificationNotes();
        
        // 检测数据格式并选择相应的填充方法
        if (this.isStructuredData(formData)) {
            // 结构化数据格式（AI增强后的格式）
            console.log('检测到结构化数据格式，使用populateFormFromData');
            
            // 先清空表单，确保完整切换
            console.log('清空表单...');
            this.clearFormCompletely();
            
            if (typeof populateFormFromData === 'function') {
                populateFormFromData(formData);
                console.log('使用populateFormFromData填充完成');
            } else {
                console.error('populateFormFromData函数不存在');
                this.manuallyPopulateForm(formData);
            }
        } else {
            // 简单键值对格式（用户原始输入格式）
            console.log('检测到简单键值对格式，使用直接填充');
            this.directPopulateForm(formData);
            console.log('直接填充完成');
        }
        
        // 更新进度显示和计数器
        if (typeof updateProgress === 'function') {
            updateProgress();
        }
        if (typeof updateMemberCount === 'function') {
            updateMemberCount();
        }
        if (typeof updateMeasureCount === 'function') {
            updateMeasureCount();
        }
        if (typeof updateD5MeasureCount === 'function') {
            updateD5MeasureCount();
        }
        if (typeof updateD6VerificationCount === 'function') {
            updateD6VerificationCount();
        }
        if (typeof updateD7PreventionCount === 'function') {
            updateD7PreventionCount();
        }
    }

    /**
     * 检测是否为结构化数据格式
     */
    isStructuredData(formData) {
        // 检查是否包含典型的结构化键名
        const structuredKeys = ['D0汇报信息', 'D1建立小组', 'D2问题描述', 'D3临时措施', 'D4根本原因', 'D5永久措施', 'D6措施验证', 'D7预防措施', 'D8庆贺团队'];
        return structuredKeys.some(key => formData.hasOwnProperty(key));
    }

    /**
     * 直接填充简单键值对格式的表单数据
     */
    directPopulateForm(formData) {
        if (!formData || typeof formData !== 'object') {
            console.log('directPopulateForm: 无有效数据');
            return;
        }
        
        console.log('使用直接填充方案，数据:', formData);
        console.log('字段数量:', Object.keys(formData).length);
        
        // 先完全清空表单，确保版本切换的完整性
        console.log('清空表单...');
        this.clearFormCompletely();
        
        let successCount = 0;
        let failCount = 0;
        
        // 填充所有字段，包括空值字段
        Object.keys(formData).forEach(key => {
            const value = formData[key];
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            
            if (element) {
                // 设置值，包括空值
                element.value = value || '';
                successCount++;
                
                if (value && value.trim() !== '') {
                    console.log(`✓ 填充字段 ${key}: "${value}"`);
                } else {
                    console.log(`✓ 清空字段 ${key}`);
                }
                
                // 如果是textarea，触发自动调整高度
                if (element.tagName === 'TEXTAREA' && typeof autoResizeTextarea === 'function') {
                    autoResizeTextarea(element);
                }
            } else {
                failCount++;
                console.warn(`✗ 找不到字段元素: ${key}`);
            }
        });
        
        console.log(`直接填充完成: 处理 ${successCount} 个字段，失败 ${failCount} 个`);
    }

    /**
     * 清空表单（用于版本切换前）
     */
    clearForm() {
        if (typeof clearForm === 'function') {
            clearForm();
        } else {
            this.clearFormCompletely();
        }
    }

    /**
     * 完全清空表单（用于版本切换和新建对话）
     */
    clearFormCompletely() {
        console.log('开始完全清空表单...');

        try {
            // 设置标志阻止自动保存
            window.isFormClearing = true;

            // 优先使用全局的clearForm函数，它更完整
            if (typeof clearForm === 'function') {
                try {
                    clearForm();
                    console.log('使用全局clearForm函数清空表单');
                } catch (clearFormError) {
                    console.warn('全局clearForm函数执行失败，使用备用方案:', clearFormError);
                    this.fallbackClearForm();
                }
            } else {
                console.log('全局clearForm函数不存在，使用备用方案');
                this.fallbackClearForm();
            }

        } catch (error) {
            console.error('清空表单时出现错误:', error);
            // 即使清空失败，也要重置标志
        } finally {
            // 稍后重置标志，允许自动保存
            setTimeout(() => {
                window.isFormClearing = false;
                console.log('表单清空完成，恢复自动保存');
            }, 100);
        }
    }

    /**
     * 备用的表单清空方法
     */
    fallbackClearForm() {
        try {
            // 备用方案：手动清空
            const form = document.querySelector('#d8-form') || document.querySelector('form');
            if (!form) {
                console.warn('找不到表单元素');
                return;
            }

            let clearedCount = 0;

            // 清空所有input、textarea、select字段
            const fields = form.querySelectorAll('input, textarea, select');
            fields.forEach(field => {
                try {
                    if (field.type !== 'button' && field.type !== 'submit' && field.type !== 'hidden') {
                        field.value = '';
                        clearedCount++;
                    }
                } catch (fieldError) {
                    console.warn('清空字段失败:', field, fieldError);
                }
            });

            console.log(`手动清空表单完成，清空了 ${clearedCount} 个字段`);

            // 触发所有textarea的高度调整
            try {
                const textareas = form.querySelectorAll('textarea');
                if (typeof autoResizeTextarea === 'function') {
                    textareas.forEach(textarea => {
                        try {
                            autoResizeTextarea(textarea);
                        } catch (resizeError) {
                            console.warn('调整textarea高度失败:', textarea, resizeError);
                        }
                    });
                }
            } catch (textareaError) {
                console.warn('处理textarea时出错:', textareaError);
            }

            // 更新进度和计数器（使用try-catch包装）
            try {
                if (typeof updateProgress === 'function') {
                    updateProgress();
                }
            } catch (progressError) {
                console.warn('更新进度时出错:', progressError);
            }

        } catch (error) {
            console.error('备用清空方案也失败了:', error);
        }
    }

    /**
     * 手动填充表单数据（备用方案）
     */
    manuallyPopulateForm(formData) {
        if (!formData || typeof formData !== 'object') return;
        
        console.log('使用手动填充备用方案');
        
        Object.keys(formData).forEach(key => {
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            if (element && formData[key] !== undefined && formData[key] !== null) {
                element.value = formData[key];
                console.log(`填充字段 ${key}:`, formData[key]);
                
                // 如果是textarea，触发自动调整高度
                if (element.tagName === 'TEXTAREA' && typeof autoResizeTextarea === 'function') {
                    autoResizeTextarea(element);
                }
            }
        });
    }

    // ==================== 事件处理器 ====================

    /**
     * 对话创建事件处理
     */
    onConversationCreated(conversation) {
        console.log('=== onConversationCreated 事件触发 ===');
        this.currentConversationId = conversation.conversation_id;
        this.refreshConversationList();
        this.renderVersionList(conversation);
        
        if (conversation.form_versions.length === 0) {
            this.clearFormCompletely();
        }
    }

    /**
     * 对话加载事件处理
     */
    onConversationLoaded(conversation) {
        this.currentConversationId = conversation.conversation_id;
        this.refreshConversationList();
        this.renderVersionList(conversation);
        
        const activeVersion = conversation.form_versions.find(v => v.is_active);
        if (activeVersion) {
            this.populateForm(activeVersion.form_data);
        }
    }

    /**
     * 版本添加事件处理
     */
    onVersionAdded(conversation, version) {
        this.renderVersionList(conversation);
        this.populateForm(version.form_data);
    }

    /**
     * 版本切换事件处理
     */
    onVersionSwitched(conversation, version) {
        console.log('=== onVersionSwitched 事件触发 ===');
        console.log('版本信息:', {
            version_id: version.version_id,
            version_name: version.version_name,
            created_by: version.created_by,
            data_keys: Object.keys(version.form_data || {}),
            data_count: Object.keys(version.form_data || {}).length
        });

        // 重新渲染版本标签
        this.renderVersionTabs(conversation);

        // 重新渲染版本列表以更新高亮状态
        this.renderVersionList(conversation);

        // 填充表单数据
        console.log('事件处理: 开始填充表单数据...');
        this.populateForm(version.form_data);

        console.log('=== 版本切换事件处理完成 ===', version.version_name);
    }

    /**
     * 版本删除事件处理
     */
    onVersionDeleted(conversation, _deletedVersionId, newActiveVersion) {
        this.renderVersionList(conversation);

        if (newActiveVersion) {
            this.populateForm(newActiveVersion.form_data);
        }
    }

    /**
     * 关闭侧边栏
     */
    closeSidebar() {
        const sidebar = document.getElementById('unified-sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar) {
            sidebar.classList.remove('mobile-open');
        }
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    /**
     * 打开侧边栏
     */
    openSidebar() {
        const sidebar = document.getElementById('unified-sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar) {
            sidebar.classList.add('mobile-open');
        }
        if (overlay) {
            overlay.classList.add('show');
        }
    }

    /**
     * 切换侧边栏状态
     */
    toggleSidebar() {
        const sidebar = document.getElementById('unified-sidebar');
        if (sidebar && sidebar.classList.contains('mobile-open')) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    /**
     * 消息添加事件处理
     */
    onMessageAdded(conversation, message) {
        const subtitle = `${conversation.form_versions.length}个版本，${conversation.chat_history.length}条消息`;
        this.updateConversationSubtitle(subtitle);
        
        if (message.sender === 'user') {
    
        }
    }



    /**
     * 调试：显示当前对话的所有版本数据
     */
    debugShowVersionData() {
        if (!this.currentConversationId) {
            console.log('没有当前对话');
            return;
        }
        
        const conversation = window.conversationManager.conversations.get(this.currentConversationId);
        if (!conversation) {
            console.log('找不到当前对话');
            return;
        }
        
        console.log('=== 当前对话的所有版本数据 ===');
        conversation.form_versions.forEach((version, index) => {
            console.log(`版本 ${index + 1}:`, {
                version_id: version.version_id,
                version_name: version.version_name,
                created_by: version.created_by,
                is_active: version.is_active,
                created_at: version.created_at,
                data_keys: Object.keys(version.form_data || {}),
                data_count: Object.keys(version.form_data || {}).length,
                sample_data: Object.keys(version.form_data || {}).slice(0, 5).reduce((obj, key) => {
                    obj[key] = version.form_data[key];
                    return obj;
                }, {})
            });
        });
        console.log('=== 版本数据显示完成 ===');
    }
}

// 全局实例
window.conversationUI = new ConversationUI();

// 全局函数（供HTML onclick使用）
function createNewConversation() {
    console.log('=== 全局createNewConversation函数被调用 ===');
    console.log('conversationUI存在:', !!window.conversationUI);
    console.log('conversationManager存在:', !!window.conversationManager);

    if (!window.conversationUI) {
        console.error('conversationUI未初始化');
        alert('系统正在初始化，请稍后再试');
        return;
    }

    // 调用创建新对话方法，但不在这里处理错误
    // 错误处理已经在createNewConversation方法内部完成
    window.conversationUI.createNewConversation().catch(error => {
        console.error('创建新对话异步错误:', error);
        // 不再显示alert，因为错误处理已经在方法内部完成
        // 这里只记录日志用于调试
    });
}

function showConversationHistory() {
    window.conversationUI.showConversationHistory();
}

function closeConversationHistoryPanel() {
    window.conversationUI.hideConversationHistory();
}

function addNewVersion() {
    console.log('=== 全局addNewVersion函数被调用 ===');

    if (!window.conversationUI) {
        console.error('conversationUI未初始化');
        alert('系统正在初始化，请稍后再试');
        return;
    }

    window.conversationUI.addNewVersion();
}

function toggleSidebar() {
    window.conversationUI.toggleSidebar();
}

function closeSidebar() {
    window.conversationUI.closeSidebar();
}

// 移动端侧边栏切换功能
function toggleSidebarCollapse() {
    const sidebar = document.getElementById('unified-sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (sidebar.classList.contains('mobile-open')) {
        // 关闭侧边栏
        sidebar.classList.remove('mobile-open');
        if (overlay) overlay.classList.remove('show');
    } else {
        // 打开侧边栏
        sidebar.classList.add('mobile-open');
        if (overlay) overlay.classList.add('show');
    }
}

// 章节导航功能
function navigateToSection(sectionId) {
    const section = document.getElementById(`section-${sectionId}`);
    if (section) {
        // 平滑滚动到目标章节
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // 使用d8_form.js中的setActiveSection函数来更新高亮状态
        if (typeof setActiveSection === 'function') {
            setActiveSection(sectionId);
        }
    }
}

// 初始化右侧导航 - 已禁用，由d8_form.js中的initNavigation处理
function initRightNavigation() {
    // 此函数已禁用，右侧导航事件绑定由d8_form.js中的initNavigation函数统一处理
    // 避免与d8_form.js中的导航逻辑冲突
    return;
}



console.log('对话UI模块加载完成');

// 页面加载完成后进行系统状态检查
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('=== 系统初始化状态检查 ===');
        console.log('conversationManager存在:', !!window.conversationManager);
        console.log('conversationUI存在:', !!window.conversationUI);
        console.log('conversationManager.conversations大小:', window.conversationManager ? window.conversationManager.conversations.size : 'N/A');
        console.log('localStorage可用:', typeof(Storage) !== "undefined");

        // 测试localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            console.log('localStorage测试: 正常');
        } catch (e) {
            console.error('localStorage测试失败:', e);
        }

        console.log('=== 系统状态检查完成 ===');
    }, 1000);
});

// 测试函数 - 可以在控制台调用
window.testVersionSwitching = function() {
    console.log('开始测试版本切换功能');
    
    if (!window.conversationUI.currentConversationId) {
        console.log('创建测试对话');
        const testData = {
            d0_title: '测试8D报告',
            d0_reporter: '测试用户',
            d0_time: '2024-07-08',
            d0_background: '这是一个测试背景',
            d2_description: '测试问题描述'
        };
        
        window.conversationUI.currentConversationId = window.conversationManager.createConversation(testData, '测试对话');
    }
    
    const conversation = window.conversationManager.conversations.get(window.conversationUI.currentConversationId);
    if (conversation && conversation.form_versions.length < 2) {
        console.log('创建第二个测试版本');
        const enhancedData = {
            d0_title: '测试8D报告 - AI增强版',
            d0_reporter: '测试用户',
            d0_time: '2024-07-08',
            d0_background: '这是一个AI增强的测试背景，包含更多详细信息',
            d2_description: '测试问题描述 - AI增强版，包含根本原因分析'
        };
        
        window.conversationManager.addFormVersion(conversation, enhancedData, 'ai');
    }
    
    console.log('测试数据创建完成，请尝试点击版本标签进行切换');
    console.log('当前对话:', conversation);
};

/**
 * 显示当前版本的修改意见
 */
ConversationUI.prototype.displayModificationNotes = function() {
    const notesDisplay = document.getElementById('modification-notes-display');
    if (!notesDisplay) return;
    
    // 获取当前活跃版本
    let currentVersion = null;
    if (this.currentConversationId) {
        const conversation = window.conversationManager.conversations.get(this.currentConversationId);
        if (conversation) {
            currentVersion = conversation.form_versions.find(v => v.is_active);
        }
    }
    
    // 显示修改意见或默认提示
    if (currentVersion && currentVersion.modification_notes) {
        // 有修改意见时显示
        notesDisplay.innerHTML = `
            <div class="modification-notes">
                <h4>📝 本版本修改要求：</h4>
                <div class="notes-content">
                    <p>${currentVersion.modification_notes}</p>
                </div>
                <div class="notes-meta">
                    <span class="notes-time">创建时间：${new Date(currentVersion.created_at).toLocaleString('zh-CN')}</span>
                    <span class="notes-author">创建者：${currentVersion.created_by === 'ai' ? 'AI优化' : '用户输入'}</span>
                </div>
            </div>
        `;
    } else {
        // 没有修改意见时显示默认提示
        const isInitialVersion = !currentVersion || 
                                currentVersion.version_name === '原始输入' || 
                                currentVersion.created_by === 'user';
        
        notesDisplay.innerHTML = `
            <div class="no-modifications">
                <p>${isInitialVersion ? '这是初始版本，暂无修改意见。' : '此版本暂无特别修改要求。'}</p>
            </div>
        `;
    }
}; 

