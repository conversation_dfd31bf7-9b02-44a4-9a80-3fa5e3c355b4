/**
 * 对话式8D报告管理系统
 * 支持多版本表单、对话历史和智能协作
 */

class ConversationManager {
    constructor() {
        this.currentConversation = null;
        this.activeVersion = null;
        this.conversations = new Map();
        this.listeners = new Map();
        
        this.initEventListeners();
        this.loadConversationsFromStorage();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 监听表单数据变化
        document.addEventListener('DOMContentLoaded', () => {
            this.bindFormEvents();
        });
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 生成对话标题
     */
    generateConversationTitle(formData = null) {
        if (formData && formData.d0_title) {
            return formData.d0_title;
        }
        
        if (formData && formData.d2_description) {
            const desc = formData.d2_description.substring(0, 20);
            return desc ? `${desc}...` : '新建8D报告';
        }
        
        const timestamp = new Date().toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        return `8D报告 ${timestamp}`;
    }

    /**
     * 创建新对话
     */
    createConversation(initialFormData = null, title = null) {
        const conversationId = this.generateId();
        const now = new Date().toISOString();
        
        const conversation = {
            conversation_id: conversationId,
            title: title || this.generateConversationTitle(initialFormData),
            created_at: now,
            updated_at: now,
            status: 'active',
            form_versions: [],
            chat_history: [],
            generated_documents: [],
            session_id: null // Dify会话ID
        };

        // 如果有初始数据，创建第一个版本
        if (initialFormData && !this.isFormEmpty(initialFormData)) {
            this.addFormVersion(conversation, initialFormData, 'user', '原始输入');
        }

        // 先将对话添加到内存中
        this.conversations.set(conversationId, conversation);
        this.currentConversation = conversation;

        // 尝试保存到localStorage，但不影响对话创建
        const saveSuccess = this.saveConversationsToStorage();
        if (!saveSuccess) {
            console.warn('对话已创建但localStorage保存失败，数据仍在内存中可用');
        }

        console.log('创建新对话:', conversation);
        this.notifyListeners('conversationCreated', conversation);
        return conversationId;
    }

    /**
     * 添加表单版本
     */
    addFormVersion(conversation, formData, createdBy = 'user', customName = null, modificationNotes = null) {
        const versionId = `v${conversation.form_versions.length + 1}`;
        const now = new Date().toISOString();
        
        let versionName;
        if (customName) {
            versionName = customName;
        } else if (createdBy === 'ai') {
            const aiVersionCount = conversation.form_versions.filter(v => v.created_by === 'ai').length;
            versionName = `AI增强版本${aiVersionCount + 1}`;
        } else {
            const userVersionCount = conversation.form_versions.filter(v => v.created_by === 'user').length;
            versionName = userVersionCount === 0 ? '原始输入' : `用户修改版本${userVersionCount}`;
        }

        // 设置所有版本为非活跃
        conversation.form_versions.forEach(v => v.is_active = false);

        const version = {
            version_id: versionId,
            version_name: versionName,
            created_at: now,
            created_by: createdBy,
            form_data: JSON.parse(JSON.stringify(formData)), // 深拷贝
            modification_notes: modificationNotes || null, // 添加修改意见字段
            is_active: true
        };

        conversation.form_versions.push(version);
        conversation.updated_at = now;
        this.activeVersion = versionId;
        
        this.saveConversationsToStorage();
        this.notifyListeners('versionAdded', { conversation, version });
        
        console.log('添加新版本:', version);
        return versionId;
    }

    /**
     * 切换到指定版本
     */
    switchToVersion(versionId) {
        if (!this.currentConversation) return false;

        const version = this.currentConversation.form_versions.find(v => v.version_id === versionId);
        if (!version) return false;

        // 设置所有版本为非活跃
        this.currentConversation.form_versions.forEach(v => v.is_active = false);
        version.is_active = true;
        this.activeVersion = versionId;

        this.saveConversationsToStorage();
        this.notifyListeners('versionSwitched', { conversation: this.currentConversation, version });
        
        console.log('切换到版本:', version);
        return true;
    }

    /**
     * 获取当前活跃版本
     */
    getActiveVersion() {
        if (!this.currentConversation) return null;
        return this.currentConversation.form_versions.find(v => v.is_active);
    }

    /**
     * 删除版本
     */
    deleteVersion(versionId) {
        if (!this.currentConversation) return false;

        const versionIndex = this.currentConversation.form_versions.findIndex(v => v.version_id === versionId);
        if (versionIndex === -1) return false;

        // 不能删除唯一的版本
        if (this.currentConversation.form_versions.length <= 1) {
            console.warn('不能删除最后一个版本');
            return false;
        }

        const deletedVersion = this.currentConversation.form_versions[versionIndex];
        const wasActive = deletedVersion.is_active;

        // 删除版本
        this.currentConversation.form_versions.splice(versionIndex, 1);

        // 如果删除的是活跃版本，需要激活另一个版本
        if (wasActive && this.currentConversation.form_versions.length > 0) {
            // 激活最后一个版本
            const lastVersion = this.currentConversation.form_versions[this.currentConversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
        }

        this.currentConversation.updated_at = new Date().toISOString();
        this.saveConversationsToStorage();
        this.notifyListeners('versionDeleted', { 
            conversation: this.currentConversation, 
            deletedVersionId: versionId,
            newActiveVersion: wasActive ? this.getActiveVersion() : null
        });
        
        console.log('删除版本成功:', versionId);
        return true;
    }

    /**
     * 添加聊天消息
     */
    addChatMessage(sender, message, versionBefore = null, versionAfter = null) {
        if (!this.currentConversation) return null;

        const messageId = this.generateId();
        const chatMessage = {
            message_id: messageId,
            sender: sender, // 'user' 或 'ai'
            message: message,
            timestamp: new Date().toISOString(),
            version_before: versionBefore,
            version_after: versionAfter
        };

        this.currentConversation.chat_history.push(chatMessage);
        this.currentConversation.updated_at = new Date().toISOString();
        
        this.saveConversationsToStorage();
        this.notifyListeners('messageAdded', { conversation: this.currentConversation, message: chatMessage });
        
        return messageId;
    }

    /**
     * 加载对话
     */
    loadConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.currentConversation = conversation;
        
        // 找到活跃版本
        const activeVersion = conversation.form_versions.find(v => v.is_active);
        if (activeVersion) {
            this.activeVersion = activeVersion.version_id;
        } else if (conversation.form_versions.length > 0) {
            // 如果没有活跃版本，激活最后一个版本
            const lastVersion = conversation.form_versions[conversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
            this.saveConversationsToStorage();
        }

        this.notifyListeners('conversationLoaded', conversation);
        console.log('加载对话:', conversation);
        return true;
    }

    /**
     * 删除对话
     */
    deleteConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.conversations.delete(conversationId);
        
        if (this.currentConversation && this.currentConversation.conversation_id === conversationId) {
            this.currentConversation = null;
            this.activeVersion = null;
        }

        this.saveConversationsToStorage();
        this.notifyListeners('conversationDeleted', conversationId);
        return true;
    }

    /**
     * 获取所有对话列表
     */
    getAllConversations() {
        return Array.from(this.conversations.values()).sort((a, b) => 
            new Date(b.updated_at) - new Date(a.updated_at)
        );
    }

    /**
     * 检查表单是否为空
     */
    isFormEmpty(formData) {
        if (!formData || typeof formData !== 'object') return true;
        
        for (const key in formData) {
            if (formData[key] && String(formData[key]).trim() !== '') {
                return false;
            }
        }
        return true;
    }

    /**
     * 从localStorage加载对话数据
     */
    loadConversationsFromStorage() {
        try {
            const stored = localStorage.getItem('8d_conversations');
            if (stored) {
                const conversationsData = JSON.parse(stored);
                this.conversations = new Map(conversationsData);
                console.log(`加载了 ${this.conversations.size} 个对话`);
            }
        } catch (error) {
            console.error('加载对话数据失败:', error);
            this.conversations = new Map();
        }
    }

    /**
     * 保存对话数据到localStorage
     */
    saveConversationsToStorage() {
        try {
            const conversationsData = Array.from(this.conversations.entries());
            const dataString = JSON.stringify(conversationsData);
            localStorage.setItem('8d_conversations', dataString);
            console.log('对话数据保存成功');
            return true;
        } catch (error) {
            console.error('保存对话数据失败:', error);

            // 如果是存储空间不足，尝试清理过期数据
            if (error.name === 'QuotaExceededError') {
                console.log('存储空间不足，尝试清理过期数据');
                this.cleanupExpiredConversations(7); // 只保留7天内的数据
                try {
                    const conversationsData = Array.from(this.conversations.entries());
                    const dataString = JSON.stringify(conversationsData);
                    localStorage.setItem('8d_conversations', dataString);
                    console.log('清理后重新保存成功');
                    return true;
                } catch (retryError) {
                    console.error('清理后仍然保存失败:', retryError);
                    // 即使保存失败，也不影响内存中的对话数据
                    console.warn('localStorage保存失败，但对话数据仍在内存中可用');
                    return false;
                }
            } else {
                // 其他类型的错误
                console.warn('localStorage保存失败，但对话数据仍在内存中可用');
                return false;
            }
        }
    }

    /**
     * 清理过期对话
     */
    cleanupExpiredConversations(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        let deletedCount = 0;
        this.conversations.forEach((conversation, id) => {
            if (new Date(conversation.updated_at) < cutoffDate) {
                this.conversations.delete(id);
                deletedCount++;
            }
        });

        if (deletedCount > 0) {
            this.saveConversationsToStorage();
            console.log(`清理了 ${deletedCount} 个过期对话`);
        }
    }

    /**
     * 添加事件监听器
     */
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(event, callback) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        if (!this.listeners.has(event)) return;
        this.listeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`事件监听器错误 (${event}):`, error);
            }
        });
    }

    /**
     * 绑定表单事件
     */
    bindFormEvents() {
        // 这里将在后续实现中绑定表单变化事件
        console.log('绑定表单事件');
    }



    /**
     * 导入对话数据
     */
    importConversation(conversationData) {
        try {
            // 生成新的ID以避免冲突
            const newId = this.generateId();
            const conversation = {
                ...conversationData,
                conversation_id: newId,
                imported_at: new Date().toISOString()
            };

            this.conversations.set(newId, conversation);
            this.saveConversationsToStorage();
            
            return newId;
        } catch (error) {
            console.error('导入对话失败:', error);
            return null;
        }
    }
}

// 全局对话管理器实例
window.conversationManager = new ConversationManager();

// 暴露给其他脚本使用
window.ConversationManager = ConversationManager; 